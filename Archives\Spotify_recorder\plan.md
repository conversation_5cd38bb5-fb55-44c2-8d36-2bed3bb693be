# Spotify Recorder Article Creation Plan

## 用户需求分析
基于 `info_aia.md` 文件的具体要求：

### 核心需求
- **主题**: Spotify recorder
- **SEO关键词**: record spotify music
- **字数要求**: 2600字（不能少，最多可超出20%，即最高3120字）
- **语言**: English
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: D (Personal Experience/Case Study Opening)

### 推荐产品
- **产品**: Cinch Audio Recorder Pro
- **价格**: $25.99 USD（不在介绍中显示价格）
- **官方页面**: https://www.cinchsolution.com/cinch-audio-recorder/
- **参考资料**: New_article/ref/car_guide.md

### 质量要求
- **四大评估维度**: Effort, Originality, Talent/Skill, Accuracy
- **信息增量**: 至少3-5个独特观点或解决方案
- **个人经验**: 基于实际使用经验的见解和试错故事
- **用户痛点**: 针对具体问题的解决方案

## 执行步骤清单

### 第1步：生成超级大纲 ✅
1. 提取参考URL的H2-H4标题
2. 合并整理类似标题
3. 按层级结构组织框架
4. 保存至 `super_outline.md`

### 第2步：创建最终大纲 ⏳
1. 基于超级大纲优化结构
2. 进行竞品内容空白分析
3. 挖掘独特价值点
4. 添加人工经验要素
5. 智能字数分配（2600字目标）
6. 保存至 `final_outline.md`

### 第3步：撰写初稿 ⏳
1. 基于最终大纲撰写内容
2. 遵循 `first_draft.md` 工作流程
3. 确保达到字数要求
4. 保存至 `first_draft.md`

### 第4步：生成SEO内容 ⏳
1. 创建SEO标题和元描述
2. 生成featured image提示词
3. 遵循 `seo_titles.md` 工作流程
4. 保存至 `seo_metadata_images.md`

### 第5步：质量检查 ⏳
1. 验证字数范围（2600-3120字）
2. 检查AI语言和句子结构
3. 验证内外部链接数量
4. 最终质量评估

## 完成标准和检查点

### 超级大纲检查点
- [ ] 已提取所有参考URL的标题
- [ ] 标题已合并整理并标记源数量
- [ ] 按层级结构重新组织
- [ ] 不包含引言和结论章节

### 最终大纲检查点
- [ ] 包含至少3个竞品未涵盖的独特观点
- [ ] 每个H2章节准备了人工经验要素
- [ ] 识别并准备解决用户痛点
- [ ] 包含可验证的准确信息
- [ ] 体现专业判断和建议
- [ ] 字数分配总和在目标范围内
- [ ] 核心推荐产品章节获得20-25%字数分配

### 初稿检查点
- [ ] 字数达到2600-3120字范围
- [ ] 使用D策略开头（个人经验/案例研究）
- [ ] 适当整合Cinch Audio Recorder Pro
- [ ] 包含个人试错故事和经验
- [ ] 避免明显AI语言特征

### SEO内容检查点
- [ ] SEO标题优化关键词"record spotify music"
- [ ] 元描述吸引点击
- [ ] Featured image提示词相关且具体

## 预期输出文件清单
1. `Spotify_recorder/plan.md` - 执行计划（本文件）
2. `Spotify_recorder/super_outline.md` - 超级大纲
3. `Spotify_recorder/final_outline.md` - 最终大纲
4. `Spotify_recorder/first_draft.md` - 文章初稿
5. `Spotify_recorder/seo_metadata_images.md` - SEO内容

## 参考资源
- 用户需求文件: `New_article/info_aia.md`
- 产品信息: `New_article/ref/car_guide.md`
- 大纲工作流程: `New_article/outline.md`
- 初稿工作流程: `New_article/first_draft.md`
- SEO工作流程: `New_article/seo_titles.md`
- 参考URL:
  - https://audials.com/en/how-to-record-music/spotify-recorder
  - https://recorder.easeus.com/screen-recording-tips/spotify-recorder.html
  - https://www.drmare.com/spotify-music/top-6-spotify-music-recorder.html
