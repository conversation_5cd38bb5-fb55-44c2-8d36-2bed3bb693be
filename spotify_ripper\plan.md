# Spotify Ripper 文章创作执行计划

## 用户需求和目标
- **文章主题**: Spotify ripper
- **SEO关键词**: rip spotify music
- **文章长度**: 2300字（不能少，最多可以超出20%，即最多2760字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据、趋势和案例研究
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **推荐产品**: Cinch Audio Recorder
- **开头策略**: D (Personal Experience/Case Study Opening)

## 内容质量评估维度
1. **Effort (努力程度)**: 内容中必须体现明显的人工成分和深度思考
2. **Originality (原创性)**: 提供独特信息增量，避免全网内容的"炒冷饭"
3. **Talent/Skill (专业能力)**: 展示作者在该领域的专业知识和实际经验
4. **Accuracy (准确性)**: 确保事实准确，避免错误信息

## 信息增量要求
- 每篇文章必须包含至少3-5个其他文章未涵盖的独特观点或解决方案
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案，而非泛泛而谈

## 需要执行的步骤清单

### 步骤1: 提取用户需求 ✅
- [x] 阅读并提取 `New_article/info_aia.md` 中的所有需求
- [x] 清晰记录需求，指导后续步骤
- [x] 确认所有需求已理解并纳入计划

### 步骤2: 生成大纲
- [ ] 分析参考URL内容，提取H2-H4级标题
- [ ] 合并整理提取的标题，创建初始大纲
- [ ] 保存初始大纲为 `super_outline.md`
- [ ] 优化大纲，创建最终版本
- [ ] 保存最终大纲为 `final_outline.md`

### 步骤3: 创作初稿
- [ ] 使用最终大纲，遵循 `New_article/first_draft.md` 的工作流程
- [ ] 撰写初稿，严格控制字数在2300-2760字范围内
- [ ] 保存初稿为 `first_draft.md`

### 步骤4: 生成SEO内容
- [ ] 遵循 `New_article/seo_titles.md` 的工作流程
- [ ] 创建SEO标题和对应的元描述
- [ ] 生成featured image图片提示词
- [ ] 保存所有SEO相关内容至 `seo_metadata_images.md`

### 步骤5: 检查
- [ ] 检查字数是否精确控制在用户要求的范围内
- [ ] 检查是否有明显AI语言和句子结构
- [ ] 检查内部链接和外部链接数量是否达标

## 完成标准和检查点

### 大纲阶段检查点
- [ ] 是否包含至少3个竞品文章未涵盖的独特观点？
- [ ] 是否为每个H2章节准备了人工经验要素？
- [ ] 是否识别并准备解决用户的具体痛点？
- [ ] 是否包含可验证的准确信息和数据？
- [ ] 是否体现了作者的专业判断和建议？

### 初稿阶段检查点
- [ ] 字数是否在2300-2760字范围内？
- [ ] 是否遵循人性化写作指南？
- [ ] 是否包含个人经验和试错故事？
- [ ] 是否自然整合了SEO关键词？
- [ ] 是否包含推荐产品的详细介绍和下载链接？

## 预期输出文件清单
1. `plan.md` - 执行计划文件 ✅
2. `super_outline.md` - 初始大纲
3. `final_outline.md` - 最终大纲
4. `first_draft.md` - 文章初稿
5. `seo_metadata_images.md` - SEO元数据和图片提示词

## 特殊要求
- 使用开头策略D：基于个人经验/案例研究的开头
- 重点推荐Cinch Audio Recorder，必须提供Windows和Mac下载链接
- 文章必须包含内部链接（来自cinchsolution.com网站地图）
- 每个H2章节需要添加相关图片
- 严格遵循人性化写作指南，避免AI腔调