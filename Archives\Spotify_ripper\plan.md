# Spotify Ripper Article Creation Plan

## 用户需求和目标
- **主题**: Spotify ripper
- **SEO关键词**: rip spotify music
- **字数要求**: 2300字（不能少，最多可超出20%，即最高2760字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: D (Personal Experience/Case Study Opening)
- **推荐产品**: Cinch Audio Recorder Pro ($25.99)

## 内容质量要求
- **Effort**: 体现明显的人工成分和深度思考
- **Originality**: 提供3-5个独特观点或解决方案
- **Talent/Skill**: 展示专业知识和实际经验
- **Accuracy**: 确保事实准确性

## 执行步骤详细清单

### 第1步：基础研究和超级大纲生成 ✅
- [ ] 提取参考URL的H2-H4标题
- [ ] 合并整理类似标题
- [ ] 创建初步框架大纲
- [ ] 保存为 `super_outline.md`

### 第2步：竞品分析和最终大纲优化 ✅
- [ ] 分析Google前10名文章内容结构
- [ ] 识别3-5个内容空白点
- [ ] 挖掘独特价值点
- [ ] 准备人工经验要素
- [ ] 字数分配和验证
- [ ] 保存为 `final_outline.md`

### 第3步：初稿创作 ✅
- [ ] 基于最终大纲撰写初稿
- [ ] 确保字数控制在2300-2760字范围
- [ ] 整合Cinch Audio Recorder产品推荐
- [ ] 保存为 `first_draft.md`

### 第4步：SEO内容生成 ✅
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image提示词
- [ ] 保存为 `seo_metadata_images.md`

### 第5步：质量检查 ✅
- [ ] 验证字数是否在要求范围内
- [ ] 检查AI语言和句子结构
- [ ] 验证内部链接和外部链接数量

## 完成标准和检查点

### 超级大纲检查点
- 包含所有参考URL的关键标题
- 标题合并合理，避免重复
- 层级结构清晰

### 最终大纲检查点
- 包含至少3个竞品未涵盖的独特观点
- 每个H2章节准备了人工经验要素
- 字数分配总和在2300-2760字范围内
- 核心推荐产品章节获得20-25%字数分配

### 初稿检查点
- 总字数2300-2760字
- 使用D策略开头（个人经验/案例研究）
- 适当整合Cinch Audio Recorder推荐
- 包含引言、结论和FAQ

### SEO内容检查点
- 标题包含主要关键词
- 元描述吸引人且包含关键词
- Featured image提示词相关且具体

## 预期输出文件清单
1. `plan.md` - 执行计划文件
2. `super_outline.md` - 基础超级大纲
3. `final_outline.md` - 最终优化大纲
4. `first_draft.md` - 完整初稿文章
5. `seo_metadata_images.md` - SEO元数据和图片提示词

## 参考资源
- 用户需求文件: `New_article/info_aia.md`
- 产品信息: `New_article/ref/car_guide.md`
- 参考URL: 
  - https://filmora.wondershare.com/audio-editing/spotify-ripper.html
  - https://www.spotikeep.com/blog/spotify-ripper.html
- 产品官网: https://www.cinchsolution.com/cinch-audio-recorder/
- 网站地图: https://www.cinchsolution.com/sitemap/
